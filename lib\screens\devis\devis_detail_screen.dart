import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/devis.dart';
import '../../models/client.dart';
import '../../providers/devis_provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../services/devis_service.dart';
import '../../widgets/professional_ui_components.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'nouveau_devis_screen.dart';

class DevisDetailScreen extends StatefulWidget {
  final String devisId;

  const DevisDetailScreen({Key? key, required this.devisId}) : super(key: key);

  @override
  State<DevisDetailScreen> createState() => _DevisDetailScreenState();
}

class _DevisDetailScreenState extends State<DevisDetailScreen> {
  Devis? _devis;
  Client? _client;
  bool _isLoading = true;
  bool _isTransforming = false;
  final DevisService _devisService = DevisService();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final devisProvider = Provider.of<DevisProvider>(context, listen: false);
      final clientProvider = Provider.of<FirebaseClientProvider>(
        context,
        listen: false,
      );

      // Charger le devis depuis la liste existante
      final devisList = devisProvider.devis;
      final devis = devisList.firstWhere(
        (d) => d.id == widget.devisId,
        orElse: () => throw Exception('Devis non trouvé'),
      );

      setState(() {
        _devis = devis;
      });

      // Charger le client
      final client = await clientProvider.getClientById(devis.clientId);
      setState(() {
        _client = client;
      });
    } catch (e) {
      print('Erreur lors du chargement des données: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _transformerEnCommande() async {
    if (_devis?.id == null || _isTransforming) return;

    setState(() {
      _isTransforming = true;
    });

    try {
      print('DEBUG: Starting transformation using provider...');
      final devisProvider = Provider.of<DevisProvider>(context, listen: false);
      final commandeId = await devisProvider.transformerEnCommande(_devis!.id!);

      if (commandeId != null) {
        print('DEBUG: Commande created with ID: $commandeId');
        print('DEBUG: Provider automatically reloaded devis list');

        if (mounted) {
          setState(() {
            _isTransforming = false;
          });

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Devis transformé en commande avec succès (ID: $commandeId)'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );

          // Wait a bit for the snackbar to show, then navigate back
          await Future.delayed(const Duration(milliseconds: 1000));

          if (mounted) {
            Navigator.of(context).pop(); // Return to previous screen
          }
        }
      } else {
        throw Exception('Échec de la transformation - ID de commande null');
      }
    } catch (e) {
      print('DEBUG ERROR: Transform failed: $e');

      if (mounted) {
        setState(() {
          _isTransforming = false;
        });

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la transformation: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _naviguerVersModification() {
    if (_devis != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => NouveauDevisScreen(devisAModifier: _devis),
        ),
      );
    }
  }

  void _confirmerSuppression() {
    if (_devis == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer le devis ${_devis!.numero} ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context); // Close dialog

              try {
                final devisProvider =
                    Provider.of<DevisProvider>(context, listen: false);
                final success = await devisProvider.supprimerDevis(_devis!.id!);

                if (success && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Devis supprimé avec succès'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  Navigator.of(context).pop(); // Return to devis list
                } else if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Erreur lors de la suppression'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} DT';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildCard(Widget child, {bool hasShadow = true}) {
    return ProfessionalCard(
      margin: const EdgeInsets.symmetric(vertical: 8),
      hasShadow: hasShadow,
      child: child,
    );
  }

  Widget _buildHeader() {
    if (_devis == null) return const SizedBox.shrink();

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header avec numéro et actions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _devis!.numeroFormate,
                      style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 20,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Créé le ${_formatDate(_devis!.dateCreation)}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              // Action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Edit button
                  Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => _naviguerVersModification(),
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                const Color(0xFF059669).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: const Color(0xFF059669)
                                  .withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: const Icon(
                            Icons.edit_outlined,
                            size: 18,
                            color: Color(0xFF059669),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Delete button
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => _confirmerSuppression(),
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                const Color(0xFFEF4444).withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: const Icon(
                          Icons.delete_outline,
                          size: 18,
                          color: Color(0xFFEF4444),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Total amount display
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF059669).withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF059669).withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Montant total',
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      _devis!.totalTTCFormate,
                      style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        color: Color(0xFF059669),
                        fontSize: 20,
                      ),
                    ),
                    Text(
                      'TTC',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Transform to order button
          if (_devis!.statut != StatutDevis.accepte &&
              _devis!.statut != StatutDevis.refuse) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: PrimaryActionButton(
                text: _isTransforming
                    ? 'Transformation en cours...'
                    : 'Transformer en commande',
                icon: _isTransforming ? null : Icons.shopping_cart_outlined,
                onPressed: _isTransforming ? null : _transformerEnCommande,
                isLoading: _isTransforming,
                isFullWidth: true,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildClientInfo() {
    if (_client == null) return const SizedBox.shrink();

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.person_outline,
                  color: Color(0xFF3B82F6),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Informations Client',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Nom', _client!.nomComplet, Icons.person),
          _buildInfoRow(
            'Téléphone',
            _client!.primaryPhone.isNotEmpty ? _client!.primaryPhone : 'N/A',
            Icons.phone,
          ),
          _buildInfoRow('Email', _client!.email, Icons.email),
          _buildInfoRow('Adresse', _client!.adresse, Icons.location_on),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              size: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildArticlesTable() {
    if (_devis == null || _devis!.items.isEmpty) {
      return _buildCard(
        Column(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun article dans ce devis',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF059669).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.inventory_2_outlined,
                  color: Color(0xFF059669),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Articles (${_devis!.items.length})',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              headingRowColor: WidgetStateProperty.all(Colors.grey[100]),
              columns: const [
                DataColumn(
                  label: Text(
                    'Article',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Qté',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Prix Unit.',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Total',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
              rows: _devis!.items.map((item) {
                return DataRow(
                  cells: [
                    DataCell(
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            item.designation,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (item.reference.isNotEmpty)
                            Text(
                              'Réf: ${item.reference}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                        ],
                      ),
                    ),
                    DataCell(Text(item.quantite.toString())),
                    DataCell(Text(_formatCurrency(item.prixUnitaireHT))),
                    DataCell(
                      Text(
                        _formatCurrency(item.sousTotal),
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummary() {
    if (_devis == null) return const SizedBox.shrink();

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.calculate_outlined,
                  color: Color(0xFFF59E0B),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Résumé Financier',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!, width: 1),
            ),
            child: Column(
              children: [
                _buildFinancialRow('Sous-total HT', _devis!.sousTotal),
                const SizedBox(height: 8),
                _buildFinancialRow(
                  'TVA (${_devis!.tauxTva.toStringAsFixed(0)}%)',
                  _devis!.montantTva,
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  child: Divider(height: 1),
                ),
                _buildFinancialRow('Total TTC', _devis!.totalTTC,
                    isTotal: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialRow(
    String label,
    double amount, {
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.w700 : FontWeight.w500,
            color: isTotal ? const Color(0xFF1F2937) : Colors.grey[700],
          ),
        ),
        Text(
          _formatCurrency(amount),
          style: TextStyle(
            fontSize: isTotal ? 18 : 14,
            fontWeight: isTotal ? FontWeight.w700 : FontWeight.w600,
            color: isTotal ? const Color(0xFF059669) : const Color(0xFF1F2937),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;
    final padding = isSmallScreen ? 16.0 : 24.0;

    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 1,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 28, showText: false),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _devis?.numero ?? 'Détails du devis',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 22,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (_devis != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            'Créé le ${_formatDate(_devis!.dateCreation)}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: isSmallScreen ? 11 : 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: _isLoading
                ? SizedBox(
                    height: MediaQuery.of(context).size.height * 0.6,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            color: const Color(0xFF3B82F6),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Chargement des détails...',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : _devis == null
                    ? SizedBox(
                        height: MediaQuery.of(context).size.height * 0.6,
                        child: const Center(
                          child: Text(
                            'Devis non trouvé',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                          ),
                        ),
                      )
                    : Padding(
                        padding: EdgeInsets.all(padding),
                        child: Column(
                          children: [
                            _buildHeader(),
                            _buildClientInfo(),
                            _buildArticlesTable(),
                            _buildFinancialSummary(),
                            const SizedBox(height: 24),
                          ],
                        ),
                      ),
          ),
        ],
      ),
    );
  }
}
