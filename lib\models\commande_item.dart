class CommandeItem {
  final int? id;
  final String? commandeId; // Nullable car sera défini lors de la sauvegarde
  final String produitId;
  final String nomProduit;
  final String? codeProduit; // Référence/code du produit
  final double prixUnitaire;
  final int quantite;
  final double sousTotal;
  final String? unite; // Unité (pièce, kg, m², etc.)
  final double remise; // Remise par article (montant ou pourcentage)
  final bool remiseEnPourcentage; // true si remise en %, false si montant fixe

  CommandeItem({
    this.id,
    this.commandeId,
    required this.produitId,
    required this.nomProduit,
    this.codeProduit,
    required this.prixUnitaire,
    required this.quantite,
    required this.sousTotal,
    this.unite,
    this.remise = 0.0,
    this.remiseEnPourcentage = false,
  });

  // Constructeur pour créer un item à partir d'un produit
  CommandeItem.fromProduit({
    this.id,
    this.commandeId,
    required this.produitId,
    required this.nomProduit,
    this.codeProduit,
    required this.prixUnitaire,
    required this.quantite,
    this.unite,
    this.remise = 0.0,
    this.remiseEnPourcentage = false,
  }) : sousTotal = calculateSousTotal(
            prixUnitaire, quantite, remise, remiseEnPourcentage);

  // Méthode statique pour calculer le sous-total avec remise
  static double calculateSousTotal(double prixUnitaire, int quantite,
      double remise, bool remiseEnPourcentage) {
    final sousTotal = prixUnitaire * quantite;
    if (remise == 0.0) return sousTotal;

    final montantRemise =
        remiseEnPourcentage ? (sousTotal * remise / 100) : remise;
    return sousTotal - montantRemise;
  }

  // Convertir un CommandeItem en Map pour la base de données
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'produitId': produitId,
      'nomProduit': nomProduit,
      'prixUnitaire': prixUnitaire,
      'quantite': quantite,
      'sousTotal': sousTotal,
      'remise': remise,
      'remiseEnPourcentage': remiseEnPourcentage ? 1 : 0,
    };

    // Only add optional fields if they're not null
    if (codeProduit != null) {
      map['codeProduit'] = codeProduit;
    }
    if (unite != null) {
      map['unite'] = unite;
    }

    return map;
  }

  // Créer un CommandeItem à partir d'une Map de la base de données
  factory CommandeItem.fromMap(Map<String, dynamic> map) {
    return CommandeItem(
      produitId: map['produitId'],
      nomProduit: map['nomProduit'],
      codeProduit: map['codeProduit'],
      prixUnitaire: map['prixUnitaire'].toDouble(),
      quantite: map['quantite'],
      sousTotal: map['sousTotal'].toDouble(),
      unite: map['unite'],
      remise: (map['remise'] ?? 0.0).toDouble(),
      remiseEnPourcentage: (map['remiseEnPourcentage'] ?? 0) == 1,
    );
  }

  // Créer une copie de l'item avec des modifications
  CommandeItem copyWith({
    int? id,
    String? commandeId,
    String? produitId,
    String? nomProduit,
    String? codeProduit,
    double? prixUnitaire,
    int? quantite,
    double? sousTotal,
    String? unite,
    double? remise,
    bool? remiseEnPourcentage,
  }) {
    return CommandeItem(
      id: id ?? this.id,
      commandeId: commandeId ?? this.commandeId,
      produitId: produitId ?? this.produitId,
      nomProduit: nomProduit ?? this.nomProduit,
      codeProduit: codeProduit ?? this.codeProduit,
      prixUnitaire: prixUnitaire ?? this.prixUnitaire,
      quantite: quantite ?? this.quantite,
      sousTotal: sousTotal ?? this.sousTotal,
      unite: unite ?? this.unite,
      remise: remise ?? this.remise,
      remiseEnPourcentage: remiseEnPourcentage ?? this.remiseEnPourcentage,
    );
  }

  // Prix unitaire formaté
  String get prixUnitaireFormate => '${prixUnitaire.toStringAsFixed(3)} DT';

  // Sous-total formaté
  String get sousTotalFormate => '${sousTotal.toStringAsFixed(3)} DT';

  // Unité formatée (par défaut "pièce")
  String get uniteFormatee => unite ?? 'pièce';

  // Référence formatée (par défaut "N/A")
  String get referenceFormatee => codeProduit ?? 'N/A';

  // Montant de la remise calculé
  double get montantRemise {
    if (remise == 0.0) return 0.0;
    final sousTotal = prixUnitaire * quantite;
    return remiseEnPourcentage ? (sousTotal * remise / 100) : remise;
  }

  // Remise formatée
  String get remiseFormatee {
    if (remise == 0.0) return '0.000 DT';
    return remiseEnPourcentage
        ? '${remise.toStringAsFixed(1)}%'
        : '${remise.toStringAsFixed(3)} DT';
  }

  // Montant de la remise formaté
  String get montantRemiseFormate => '${montantRemise.toStringAsFixed(3)} DT';

  @override
  String toString() {
    return 'CommandeItem{produit: $nomProduit, quantite: $quantite, sousTotal: $sousTotal}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommandeItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
